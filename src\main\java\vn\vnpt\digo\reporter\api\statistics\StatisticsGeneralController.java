package vn.vnpt.digo.reporter.api.statistics;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.configurationprocessor.json.JSONException;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import vn.vnpt.digo.reporter.dto.qni.DetailGeneralReportDto;
import vn.vnpt.digo.reporter.dto.qni.GeneralReportDto;
import vn.vnpt.digo.reporter.service.statistics.StatisticsGeneralService;

import javax.servlet.http.HttpServletRequest;
import java.util.List;


@RestController
@RequestMapping("/statistics")
public class StatisticsGeneralController {

    Logger logger = LoggerFactory.getLogger(StatisticsGeneralController.class);

    @Autowired
    private StatisticsGeneralService statisticsGeneralService;


    @GetMapping(value = "")
    public List<GeneralReportDto.CustomSummary> getStatistics(HttpServletRequest request,
                                                              @RequestParam(value = "from-date", required = true) String fromDate,
                                                              @RequestParam(value = "to-date", required = true) String toDate,
                                                              @RequestParam(value = "agency-id", required = true) List<String> agencyIds,
                                                              @RequestParam(value = "report-type-id", required = false) Integer reportTypeId,
                                                              @RequestParam(value = "sector-id", required = false) List<String> sectorId,
                                                              @RequestParam(value = "procedure-id", required = false) List<String> procedureId,
                                                              @RequestParam(value = "include-inactive", required = false) Boolean includeInactiveAgency) {
        String requestPath = request.getMethod() + " " + request.getRequestURI() + (request.getQueryString() != null ? "?" + request.getQueryString() : "");
        logger.info("DIGO-Info: " + requestPath);

        List<GeneralReportDto.CustomSummary> reportResult = null;
        reportResult = statisticsGeneralService.getGeneralReportDto(agencyIds, fromDate, toDate, reportTypeId, sectorId, procedureId, includeInactiveAgency);

        logger.info("DIGO-Info: " + reportResult.size());
        return reportResult;
    }

    @GetMapping(value = "/--detail")
    public Page<DetailGeneralReportDto.PageResult> getDetail(HttpServletRequest request,
                                                             @RequestParam(value = "from-date", required = true) String fromDate,
                                                             @RequestParam(value = "to-date", required = true) String toDate,
                                                             @RequestParam(value = "agency-id", required = true) List<String> agencyIds,
                                                             @RequestParam(value = "report-type-id", required = false) Integer reportTypeId,
                                                             @RequestParam(value = "sector-id", required = false) List<String> sectorId,
                                                             @RequestParam(value = "procedure-id", required = false) List<String> procedureId,
                                                             @RequestParam(value = "type", required = false) Integer type,
                                                             Pageable pageable
    ) {
        try {
            String requestPath = request.getMethod() + " " + request.getRequestURI() + (request.getQueryString() != null ? "?" + request.getQueryString() : "");
            logger.info("DIGO-Info: " + requestPath);

            Page<DetailGeneralReportDto.PageResult> results = null;
            results = statisticsGeneralService.getGeneralReportDetailDto(agencyIds,
                    fromDate, toDate,
                    reportTypeId, sectorId,
                    procedureId, type, pageable);

            logger.info("DIGO-Info: " + results.getTotalElements());
            return results;
        } catch (Exception e) {
            logger.error("DIGO-Info: " + e.getMessage());
        }

        return null;
    }

    @GetMapping(value = "/--detail/--all")
    public List<DetailGeneralReportDto.PageResult> getDetailAll(HttpServletRequest request,
                                                                                @RequestParam(value = "from-date", required = true) String fromDate,
                                                                                @RequestParam(value = "to-date", required = true) String toDate,
                                                                                @RequestParam(value = "agency-id", required = true) List<String> agencyIds,
                                                                                @RequestParam(value = "report-type-id", required = false) Integer reportTypeId,
                                                                                @RequestParam(value = "sector-id", required = false) List<String> sectorId,
                                                                                @RequestParam(value = "procedure-id", required = false) List<String> procedureId,
                                                                                @RequestParam(value = "type", required = false) Integer type) {
        try {
            String requestPath = request.getMethod() + " " + request.getRequestURI() + (request.getQueryString() != null ? "?" + request.getQueryString() : "");
            logger.info("DIGO-Info: " + requestPath);

            List<DetailGeneralReportDto.PageResult> results = null;
            results = statisticsGeneralService.getAllGeneralReportDetailDto(agencyIds,
                    fromDate, toDate,
                    reportTypeId, sectorId,
                    procedureId, type);

            return results;
        } catch (Exception e) {
            logger.error("DIGO-Info: " + e.getMessage());
        }
        return null;
    }

    @GetMapping("/--detail/--export")
    public ResponseEntity<Object> exportDossierStatisticGeneralDetail(
            HttpServletRequest request,
            @RequestParam(value = "from-date") String fromDate,
            @RequestParam(value = "to-date") String toDate,
            @RequestParam(value = "agency-id") List<String> agencyId,
            @RequestParam(value = "sector-id", required = false) List<String> sectorId,
            @RequestParam(value = "procedure-id", required = false) List<String> procedureId,
            @RequestParam(value = "type", required = false) Integer type,
            @RequestParam(value = "report-type-id", required = false) Integer reportTypeId
    ) throws JSONException {
        String requestPath = request.getMethod() + " " + request.getRequestURI() + (request.getQueryString() != null ? "?" + request.getQueryString() : "");
        logger.info("DIGO-Request: " + requestPath);
        ResponseEntity<Object> result = null;

        result = statisticsGeneralService.exportGeneralReportDetail(fromDate, toDate, agencyId, sectorId, procedureId, type, reportTypeId);

        return result;
    }

}
